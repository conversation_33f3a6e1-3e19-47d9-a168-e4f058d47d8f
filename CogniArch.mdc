# 🏗️ CogniArch 战略蓝图：数据库迁移表程序创建

## 当前任务状态
**任务**: 数据库迁移表程序创建
**状态**: 战略规划制定
**最后更新**: 2025-01-28

---

# 🏗️ 数据库迁移表程序创建战略蓝图 V1.0

## 📋 项目概述

### 🎯 核心目标
基于 `@.cursor/rules/index.mdc` 和 `@.cursor/rules/dev-api-guidelines-add.mdc` 规范，为AI工具平台创建完整的数据库迁移表程序，确保所有表结构符合项目规范要求。

### 🔧 技术约束
- **框架**: Laravel/Lumen 10.x
- **数据库**: MySQL 8.0.12
- **表前缀**: `p_` (框架自动添加，迁移程序中使用不带前缀的表名)
- **必需字段**: `id`、`created_at`、`updated_at`
- **备注规范**: 所有字段和表名必须使用 `->comment()` 方法添加备注
- **目标目录**: `php/api/database/migrations`

### 📊 项目范围
- **总表数量**: 30+个数据表
- **分阶段实施**: 10个阶段，按业务优先级排序
- **影响评估**: 低风险，新建表结构

---

## 🗂️ 数据库表结构规划

### 第一阶段：基础业务表 (4个表)
**优先级**: 🔴 最高 - MVP核心功能

#### 1.1 用户管理表
- **p_users**: 用户基础信息表
  - 字段: id, username, password, email, phone, avatar, status, login_count, last_login_at, ip, platform, created_at, updated_at
  - 索引: username_unique, email_unique, status, platform

#### 1.2 积分系统表
- **p_points_transactions**: 积分交易流水表
  - 字段: id, user_id, type, amount, balance_before, balance_after, description, reference_id, reference_type, created_at, updated_at
  - 索引: user_id, type, reference_id+reference_type

- **p_points_freeze**: 积分冻结表
  - 字段: id, user_id, amount, reason, reference_id, reference_type, status, expires_at, created_at, updated_at
  - 索引: user_id, status, expires_at

#### 1.3 会员系统表
- **p_memberships**: 会员等级表
  - 字段: id, user_id, level, expires_at, benefits, usage_count, max_usage, status, created_at, updated_at
  - 索引: user_id, level, status, expires_at

### 第二阶段：AI生成核心表 (6个表)
**优先级**: 🟠 高 - 核心AI功能

#### 2.1 AI内容库表
- **p_ai_music**: AI音乐库表 (MiniMax平台)
- **p_ai_sound**: AI音效库表 (火山引擎豆包平台)
- **p_ai_timbre**: AI音色库表 (双平台支持)
- **p_ai_style**: 剧情风格库表
- **p_ai_story**: AI故事库表
- **p_ai_character**: AI角色库表

### 第三阶段：资源管理表 (5个表)
**优先级**: 🟡 中高 - 资源管理功能

#### 3.1 角色库管理
- **p_character_library**: 角色库主表
- **p_user_character_bindings**: 用户角色绑定表
- **p_character_categories**: 角色分类表

#### 3.2 核心资源管理
- **p_ai_resources**: AI生成资源表
- **p_resource_versions**: 资源版本表

### 第四阶段：任务管理表 (2个表)
**优先级**: 🟡 中高 - 任务处理功能

- **p_ai_generation_tasks**: AI生成任务表
- **p_websocket_sessions**: WebSocket会话表

### 第五阶段：项目协作表 (1个表)
**优先级**: 🟡 中 - 项目管理功能

- **p_projects**: 项目管理表

### 第六阶段：作品发布表 (4个表)
**优先级**: 🔵 中低 - 增值服务功能

- **p_work_plaza**: 作品广场表
- **p_user_works**: 用户作品表
- **p_work_shares**: 作品分享表
- **p_work_interactions**: 作品互动表

### 第七阶段：高级功能表 (4个表)
**优先级**: 🔵 中低 - 高级功能

- **p_user_growth_paths**: 用户成长路径表
- **p_user_recommendations**: 个性化推荐表
- **p_referral_codes**: 邀请码表
- **p_download_tracking**: 下载跟踪表

### 第八阶段：系统管理表 (3个表)
**优先级**: 🟢 低 - 系统管理功能

- **p_ai_model_configs**: AI模型配置表
- **p_cache_statistics**: 缓存统计表
- **p_workflow_templates**: 工作流模板表

### 第九阶段：业务支持表 (4个表)
**优先级**: 🟢 低 - 业务支持功能

- **p_business_logs**: 业务日志表
- **p_user_favorites**: 用户收藏表
- **p_membership_usage_logs**: 会员使用记录表

### 第十阶段：用户素材管理表 (3个表)
**优先级**: 🟢 低 - 用户素材功能

- **p_user_assets**: 用户素材表
- **p_user_asset_categories**: 用户素材分类表
- **p_user_asset_tags**: 用户素材标签表

---

## 🛠️ 实施策略

### 迁移文件命名规范
```
YYYY_MM_DD_HHMMSS_create_{table_name}_table.php
```

### 迁移文件结构模板
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('{table_name}', function (Blueprint $table) {
            $table->id()->comment('主键ID');
            // 业务字段...
            $table->timestamps();

            // 索引定义...

            // 表备注
            $table->charset = config('database.connections.'.config('database.default').'.charset'). ' COMMENT="{表中文名称}"';
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('{table_name}');
    }
};
```

### 字段定义规范
```php
// 基础字段
$table->id()->comment('主键ID');
$table->timestamps(); // created_at, updated_at

// 字符串字段
$table->string('name', 100)->comment('名称');
$table->string('email')->unique()->comment('邮箱地址');

// 数值字段
$table->integer('user_id')->unsigned()->comment('用户ID');
$table->decimal('amount', 10, 2)->default(0.00)->comment('金额');

// 枚举字段
$table->tinyInteger('status')->default(1)->comment('状态[0=禁用,1=启用]');

// 时间字段
$table->timestamp('expires_at')->nullable()->comment('过期时间');

// JSON字段
$table->json('config')->nullable()->comment('配置信息');

// 文本字段
$table->text('description')->nullable()->comment('描述');
```

### 索引定义规范
```php
// 单字段索引
$table->index('user_id');
$table->index('status');

// 复合索引
$table->index(['user_id', 'type']);
$table->index(['status', 'created_at']);

// 唯一索引
$table->unique('email');
$table->unique(['user_id', 'resource_id']);

// 外键约束
$table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
```

---

## 📊 关键里程碑

### 里程碑 1: 基础业务表创建 (第1阶段)
- **交付物**: 4个基础业务表迁移文件
- **验收标准**: 所有表创建成功，字段备注完整，索引正确
- **预估工期**: 2天

### 里程碑 2: AI核心功能表创建 (第2阶段)
- **交付物**: 6个AI生成相关表迁移文件
- **验收标准**: AI功能相关表结构完整，支持多平台配置
- **预估工期**: 3天

### 里程碑 3: 资源管理表创建 (第3阶段)
- **交付物**: 5个资源管理表迁移文件
- **验收标准**: 资源管理功能表结构完整，关联关系正确
- **预估工期**: 2.5天

### 里程碑 4: 任务管理表创建 (第4阶段)
- **交付物**: 2个任务管理表迁移文件
- **验收标准**: 任务处理和WebSocket功能支持完整
- **预估工期**: 1天

### 里程碑 5: 完整系统表创建 (第5-10阶段)
- **交付物**: 剩余所有表迁移文件
- **验收标准**: 完整的30+表系统，所有功能模块支持完整
- **预估工期**: 5天

---

## 🔍 质量保证措施

### 代码规范检查
- ✅ 所有字段必须有 `->comment()` 备注
- ✅ 表名必须有中文备注
- ✅ 必需字段 `id`、`created_at`、`updated_at` 存在
- ✅ 索引定义合理，性能优化到位
- ✅ 外键关联关系正确

### 测试验证
- ✅ 迁移文件语法正确
- ✅ 数据库表创建成功
- ✅ 表结构符合业务需求
- ✅ 索引创建正确
- ✅ 回滚功能正常

### 文档完整性
- ✅ 每个表的字段说明文档
- ✅ 表关联关系图
- ✅ 索引使用说明
- ✅ 迁移执行指南

---

## ⚠️ 风险评估与控制

### 风险等级: 🟢 低风险

### 主要风险点
1. **表结构设计风险** (概率: 低, 影响: 中)
   - **风险描述**: 表结构设计可能不满足未来业务需求
   - **缓解措施**: 基于现有业务需求和规范文档设计，预留扩展字段
   - **应急预案**: 通过新的迁移文件进行表结构调整

2. **字段类型选择风险** (概率: 低, 影响: 低)
   - **风险描述**: 字段类型选择不当可能影响性能或数据存储
   - **缓解措施**: 严格按照Laravel最佳实践选择字段类型
   - **应急预案**: 通过迁移文件修改字段类型

3. **索引设计风险** (概率: 低, 影响: 中)
   - **风险描述**: 索引设计不合理可能影响查询性能
   - **缓解措施**: 基于业务查询场景设计索引
   - **应急预案**: 通过新迁移文件添加或删除索引

### 风险监控指标
- 迁移文件语法错误率 = 0%
- 表创建成功率 = 100%
- 字段备注完整率 = 100%
- 索引创建成功率 = 100%

---

## 📈 项目时间线

```
阶段1: [████████████████████████████████████████████████████████] 基础业务表 (2天)
阶段2: [████████████████████████████████████████████████████████] AI核心功能表 (3天)
阶段3: [████████████████████████████████████████████████████████] 资源管理表 (2.5天)
阶段4: [████████████████████████████████████████████████████████] 任务管理表 (1天)
阶段5: [████████████████████████████████████████████████████████] 完整系统表 (5天)
```

**总工期**: 13.5个工作日
**关键里程碑**:
- Day 2: 完成基础业务表创建
- Day 5: 完成AI核心功能表创建
- Day 7.5: 完成资源管理表创建
- Day 8.5: 完成任务管理表创建
- Day 13.5: 完成所有表创建

---

## 📊 成功标准

### 技术指标
- ✅ 30+个数据表100%创建完成
- ✅ 所有表结构符合Laravel规范
- ✅ 字段备注完整率100%
- ✅ 表备注完整率100%
- ✅ 索引设计合理性100%验证
- ✅ 迁移文件语法正确率100%

### 质量指标
- ✅ 迁移执行成功率100%
- ✅ 表创建成功率100%
- ✅ 回滚功能正常率100%
- ✅ 代码审查通过率100%
- ✅ 规范遵循度100%

### 业务指标
- ✅ 数据库文档更新完成
- ✅ 开发团队培训完成
- ✅ 表结构设计文档完整
- ✅ 系统架构支持完整

### 项目管理指标
- ✅ 13.5天工期按时完成
- ✅ 10个阶段按优先级完成
- ✅ 风险控制措施100%执行
- ✅ 30+个表逐个设计完成

---

## 🎯 核心资产与准则应用

### 应用的规则知识
- **@.cursor/rules/index.mdc**: 遵循项目基础规范和数据库标准
- **@.cursor/rules/dev-api-guidelines-add.mdc**: 严格按照数据库迁移规范执行

### 项目备忘应用
- **PHP命令路径**: 使用 `api` -> `@php/api/` 路径结构
- **数据库规范**: 表前缀 `p_`，迁移程序必需字段规范
- **迁移执行**: 在api目录下执行 `php artisan migrate` 命令

### 行为准则遵循
- **绝对诚实**: 所有设计决策和风险如实报告
- **环境洁癖**: 测试完成后清理所有临时文件和测试数据
- **影响性分析**: 表结构设计前进行充分的业务影响分析
- **强制性问题解决**: 遇到设计问题必须提供解决方案

---

## 📋 接管指令

**@CogniAud**: 请对本数据库迁移表程序创建战略蓝图进行规划审计，重点关注：

1. **技术方案完整性**: 30+个表的设计方案是否完整可行
2. **规范遵循度**: 是否100%遵循 `@.cursor/rules/index.mdc` 和 `@.cursor/rules/dev-api-guidelines-add.mdc` 规范
3. **表结构合理性**: 字段设计、索引设计、关联关系是否合理
4. **实施计划可行性**: 10个阶段的划分和13.5天工期是否现实
5. **质量保证充分性**: 检查测试覆盖率和质量标准是否符合要求
6. **风险评估准确性**: 评估低风险等级和控制措施是否合理
7. **迁移文件规范**: 确认迁移文件结构模板是否符合Laravel最佳实践

审计通过后，请制定详细的 **[审计清单]** 供 @CogniDev 按阶段顺序逐个创建迁移文件。

**@CogniDev**: 请等待CogniAud完成规划审计并制定审计清单后，按照清单顺序逐个阶段创建迁移文件并汇报进度。

---

**CogniArch 签名**: ✅ 数据库迁移表程序创建战略蓝图制定完成
**文档版本**: V1.0（基于30+表系统设计）
**制定时间**: 2025-01-28
**创建范围**: 30+个数据表迁移文件
**分阶段计划**: 10个阶段按业务优先级
**预估工期**: 13.5天
**下次审查**: 项目完成后